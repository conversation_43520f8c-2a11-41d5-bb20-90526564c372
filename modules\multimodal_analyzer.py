# نظام تحليل متعدد الوسائط - Vision AI + OCR + Video Understanding
import asyncio
import json
import time
import base64
import hashlib
import cv2
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import tempfile
import os

# مكتبات الذكاء الاصطناعي والرؤية الحاسوبية
try:
    from PIL import Image, ImageEnhance, ImageFilter
    import pytesseract
    from transformers import pipeline, BlipProcessor, BlipForConditionalGeneration
    import torch
    import cv2
    import numpy as np
    from moviepy.editor import VideoFileClip
    import speech_recognition as sr
    from googletrans import Translator
    import easyocr
    HAS_VISION_LIBS = True
except ImportError:
    HAS_VISION_LIBS = False

    # إنشاء بدائل وهمية
    class DummyImage:
        def open(self, *args, **kwargs):
            return self
        def convert(self, *args, **kwargs):
            return self
        def save(self, *args, **kwargs):
            pass
        @property
        def size(self):
            return (100, 100)
        @property
        def mode(self):
            return "RGB"
        def thumbnail(self, *args, **kwargs):
            pass

    Image = DummyImage()
    cv2 = None
    torch = None

from .logger import logger
from config.settings import BotConfig

class MediaType(Enum):
    """أنواع الوسائط"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    SCREENSHOT = "screenshot"
    GIF = "gif"

class AnalysisType(Enum):
    """أنواع التحليل"""
    OCR = "ocr"                    # استخراج النص
    OBJECT_DETECTION = "objects"   # كشف الكائنات
    SCENE_DESCRIPTION = "scene"    # وصف المشهد
    FACE_DETECTION = "faces"       # كشف الوجوه
    TEXT_TRANSLATION = "translate" # ترجمة النص
    AUDIO_TRANSCRIPTION = "audio"  # تحويل الصوت لنص
    SENTIMENT_ANALYSIS = "sentiment" # تحليل المشاعر

@dataclass
class MediaAnalysisResult:
    """نتيجة تحليل الوسائط"""
    media_type: MediaType
    analysis_type: AnalysisType
    content: str
    confidence: float
    metadata: Dict[str, Any]
    processing_time: float
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class MediaAnalysisRequest:
    """طلب تحليل الوسائط"""
    media_path: str
    media_type: MediaType
    analysis_types: List[AnalysisType]
    language: str = "ar"
    enhance_quality: bool = True
    extract_gaming_content: bool = True

class MultimodalAnalyzer:
    """محلل متعدد الوسائط متقدم"""
    
    def __init__(self):
        self.enabled = HAS_VISION_LIBS
        self.models = {}
        self.processors = {}
        
        # إحصائيات
        self.stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'avg_processing_time': 0,
            'by_media_type': {},
            'by_analysis_type': {},
            'last_update': datetime.now()
        }
        
        # إعدادات
        self.config = {
            'max_image_size': (1920, 1080),
            'video_frame_interval': 5,  # تحليل إطار كل 5 ثوان
            'audio_chunk_duration': 30,  # تحليل الصوت كل 30 ثانية
            'ocr_languages': ['ar', 'en'],
            'confidence_threshold': 0.7,
            'gaming_keywords': [
                'game', 'gaming', 'player', 'level', 'score', 'achievement',
                'لعبة', 'لاعب', 'مستوى', 'نقاط', 'إنجاز', 'فوز', 'خسارة'
            ]
        }
        
        if self.enabled:
            self._initialize_models()
        else:
            logger.warning("⚠️ مكتبات الرؤية الحاسوبية غير متوفرة - المحلل معطل")
    
    def _initialize_models(self):
        """تهيئة النماذج"""
        try:
            logger.info("🤖 تهيئة نماذج التحليل متعدد الوسائط...")
            
            # نموذج وصف الصور
            try:
                self.processors['image_caption'] = BlipProcessor.from_pretrained(
                    "Salesforce/blip-image-captioning-base")
                self.models['image_caption'] = BlipForConditionalGeneration.from_pretrained(
                    "Salesforce/blip-image-captioning-base")
                logger.info("✅ تم تحميل نموذج وصف الصور")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحميل نموذج وصف الصور: {e}")
            
            # نموذج كشف الكائنات
            try:
                self.models['object_detection'] = pipeline(
                    "object-detection", 
                    model="facebook/detr-resnet-50"
                )
                logger.info("✅ تم تحميل نموذج كشف الكائنات")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحميل نموذج كشف الكائنات: {e}")
            
            # محرك OCR
            try:
                self.ocr_reader = easyocr.Reader(self.config['ocr_languages'])
                logger.info("✅ تم تحميل محرك OCR")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحميل محرك OCR: {e}")
                self.ocr_reader = None
            
            # محرك التعرف على الكلام
            try:
                self.speech_recognizer = sr.Recognizer()
                logger.info("✅ تم تحميل محرك التعرف على الكلام")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحميل محرك التعرف على الكلام: {e}")
                self.speech_recognizer = None
            
            # مترجم
            try:
                self.translator = Translator()
                logger.info("✅ تم تحميل المترجم")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحميل المترجم: {e}")
                self.translator = None
            
            logger.info("✅ تم تهيئة نماذج التحليل بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة النماذج: {e}")
            self.enabled = False
    
    async def analyze_media(self, request: MediaAnalysisRequest) -> List[MediaAnalysisResult]:
        """تحليل الوسائط الرئيسي"""
        if not self.enabled:
            return []
        
        try:
            start_time = time.time()
            self.stats['total_analyses'] += 1
            
            logger.info(f"🔍 بدء تحليل الوسائط: {request.media_path}")
            logger.info(f"📊 نوع الوسائط: {request.media_type.value}")
            logger.info(f"🎯 أنواع التحليل: {[t.value for t in request.analysis_types]}")
            
            results = []
            
            # تحسين جودة الوسائط إذا طُلب ذلك
            if request.enhance_quality:
                enhanced_path = await self._enhance_media_quality(request.media_path, request.media_type)
                if enhanced_path:
                    request.media_path = enhanced_path
            
            # تنفيذ التحليلات المطلوبة
            for analysis_type in request.analysis_types:
                try:
                    result = await self._perform_analysis(request, analysis_type)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"❌ خطأ في تحليل {analysis_type.value}: {e}")
                    self.stats['failed_analyses'] += 1
            
            # تحديث الإحصائيات
            processing_time = time.time() - start_time
            self.stats['successful_analyses'] += 1
            self.stats['avg_processing_time'] = (
                self.stats['avg_processing_time'] + processing_time) / 2
            
            # إحصائيات حسب النوع
            media_type_key = request.media_type.value
            if media_type_key not in self.stats['by_media_type']:
                self.stats['by_media_type'][media_type_key] = 0
            self.stats['by_media_type'][media_type_key] += 1
            
            logger.info(f"✅ تم تحليل الوسائط بنجاح - {len(results)} نتيجة في {processing_time:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الوسائط: {e}")
            self.stats['failed_analyses'] += 1
            return []
    
    async def _enhance_media_quality(self, media_path: str, media_type: MediaType) -> Optional[str]:
        """تحسين جودة الوسائط"""
        try:
            if media_type == MediaType.IMAGE:
                return await self._enhance_image_quality(media_path)
            elif media_type == MediaType.VIDEO:
                return await self._enhance_video_quality(media_path)
            else:
                return media_path
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين جودة الوسائط: {e}")
            return media_path
    
    async def _enhance_image_quality(self, image_path: str) -> Optional[str]:
        """تحسين جودة الصورة"""
        try:
            # فتح الصورة
            image = Image.open(image_path)
            
            # تحسين الحدة
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)
            
            # تحسين التباين
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            # تحسين السطوع
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.05)
            
            # تغيير الحجم إذا كان كبيراً جداً
            if image.size[0] > self.config['max_image_size'][0] or \
               image.size[1] > self.config['max_image_size'][1]:
                image.thumbnail(self.config['max_image_size'], Image.Resampling.LANCZOS)
            
            # حفظ الصورة المحسنة
            enhanced_path = image_path.replace('.', '_enhanced.')
            image.save(enhanced_path, quality=95, optimize=True)
            
            logger.debug(f"✨ تم تحسين جودة الصورة: {enhanced_path}")
            return enhanced_path
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الصورة: {e}")
            return image_path

    async def _perform_analysis(self, request: MediaAnalysisRequest, analysis_type: AnalysisType) -> Optional[MediaAnalysisResult]:
        """تنفيذ تحليل محدد"""
        try:
            start_time = time.time()

            if analysis_type == AnalysisType.OCR:
                result = await self._perform_ocr(request)
            elif analysis_type == AnalysisType.SCENE_DESCRIPTION:
                result = await self._describe_scene(request)
            elif analysis_type == AnalysisType.OBJECT_DETECTION:
                result = await self._detect_objects(request)
            elif analysis_type == AnalysisType.AUDIO_TRANSCRIPTION:
                result = await self._transcribe_audio(request)
            elif analysis_type == AnalysisType.TEXT_TRANSLATION:
                result = await self._translate_content(request)
            else:
                return None

            if result:
                result.processing_time = time.time() - start_time

                # تحديث إحصائيات نوع التحليل
                analysis_key = analysis_type.value
                if analysis_key not in self.stats['by_analysis_type']:
                    self.stats['by_analysis_type'][analysis_key] = 0
                self.stats['by_analysis_type'][analysis_key] += 1

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ التحليل {analysis_type.value}: {e}")
            return None

    async def _perform_ocr(self, request: MediaAnalysisRequest) -> Optional[MediaAnalysisResult]:
        """استخراج النص من الصور"""
        try:
            if not self.ocr_reader:
                return None

            # قراءة النص باستخدام EasyOCR
            results = self.ocr_reader.readtext(request.media_path)

            extracted_texts = []
            total_confidence = 0

            for (bbox, text, confidence) in results:
                if confidence > self.config['confidence_threshold']:
                    extracted_texts.append(text)
                    total_confidence += confidence

            if not extracted_texts:
                return None

            combined_text = ' '.join(extracted_texts)
            avg_confidence = total_confidence / len(extracted_texts)

            # فلترة المحتوى المتعلق بالألعاب
            gaming_relevance = 0
            if request.extract_gaming_content:
                for keyword in self.config['gaming_keywords']:
                    if keyword.lower() in combined_text.lower():
                        gaming_relevance += 1

            metadata = {
                'text_blocks': len(extracted_texts),
                'gaming_keywords_found': gaming_relevance,
                'language_detected': 'mixed',
                'bbox_info': [bbox for bbox, _, _ in results]
            }

            return MediaAnalysisResult(
                media_type=request.media_type,
                analysis_type=AnalysisType.OCR,
                content=combined_text,
                confidence=avg_confidence,
                metadata=metadata,
                processing_time=0
            )

        except Exception as e:
            logger.error(f"❌ خطأ في OCR: {e}")
            return None

    async def _describe_scene(self, request: MediaAnalysisRequest) -> Optional[MediaAnalysisResult]:
        """وصف المشهد في الصورة"""
        try:
            if 'image_caption' not in self.models or 'image_caption' not in self.processors:
                return None

            # تحميل الصورة
            image = Image.open(request.media_path).convert('RGB')

            # معالجة الصورة
            inputs = self.processors['image_caption'](image, return_tensors="pt")

            # توليد الوصف
            with torch.no_grad():
                outputs = self.models['image_caption'].generate(**inputs, max_length=50)

            description = self.processors['image_caption'].decode(outputs[0], skip_special_tokens=True)

            # ترجمة الوصف للعربية إذا أمكن
            if self.translator and request.language == 'ar':
                try:
                    translated = self.translator.translate(description, dest='ar')
                    arabic_description = translated.text
                except:
                    arabic_description = description
            else:
                arabic_description = description

            # تحليل المحتوى المتعلق بالألعاب
            gaming_score = 0
            for keyword in self.config['gaming_keywords']:
                if keyword.lower() in description.lower():
                    gaming_score += 1

            metadata = {
                'original_description': description,
                'arabic_description': arabic_description,
                'gaming_relevance_score': gaming_score,
                'image_size': image.size,
                'image_mode': image.mode
            }

            return MediaAnalysisResult(
                media_type=request.media_type,
                analysis_type=AnalysisType.SCENE_DESCRIPTION,
                content=arabic_description,
                confidence=0.8,  # ثقة افتراضية
                metadata=metadata,
                processing_time=0
            )

        except Exception as e:
            logger.error(f"❌ خطأ في وصف المشهد: {e}")
            return None

    async def _detect_objects(self, request: MediaAnalysisRequest) -> Optional[MediaAnalysisResult]:
        """كشف الكائنات في الصورة"""
        try:
            if 'object_detection' not in self.models:
                return None

            # تحميل الصورة
            image = Image.open(request.media_path)

            # كشف الكائنات
            results = self.models['object_detection'](image)

            detected_objects = []
            gaming_objects = []

            for result in results:
                if result['score'] > self.config['confidence_threshold']:
                    obj_label = result['label']
                    detected_objects.append({
                        'label': obj_label,
                        'confidence': result['score'],
                        'bbox': result['box']
                    })

                    # فحص الكائنات المتعلقة بالألعاب
                    gaming_keywords = ['computer', 'monitor', 'keyboard', 'mouse', 'controller', 'screen']
                    if any(keyword in obj_label.lower() for keyword in gaming_keywords):
                        gaming_objects.append(obj_label)

            if not detected_objects:
                return None

            # إنشاء وصف نصي للكائنات
            object_labels = [obj['label'] for obj in detected_objects]
            content = f"تم اكتشاف الكائنات التالية: {', '.join(object_labels)}"

            metadata = {
                'objects_detected': detected_objects,
                'gaming_objects': gaming_objects,
                'total_objects': len(detected_objects),
                'gaming_relevance': len(gaming_objects) > 0
            }

            avg_confidence = sum(obj['confidence'] for obj in detected_objects) / len(detected_objects)

            return MediaAnalysisResult(
                media_type=request.media_type,
                analysis_type=AnalysisType.OBJECT_DETECTION,
                content=content,
                confidence=avg_confidence,
                metadata=metadata,
                processing_time=0
            )

        except Exception as e:
            logger.error(f"❌ خطأ في كشف الكائنات: {e}")
            return None

    async def _transcribe_audio(self, request: MediaAnalysisRequest) -> Optional[MediaAnalysisResult]:
        """تحويل الصوت إلى نص"""
        try:
            if not self.speech_recognizer:
                return None

            # استخراج الصوت من الفيديو إذا لزم الأمر
            audio_path = request.media_path
            if request.media_type == MediaType.VIDEO:
                audio_path = await self._extract_audio_from_video(request.media_path)
                if not audio_path:
                    return None

            # تحويل الصوت إلى نص
            with sr.AudioFile(audio_path) as source:
                audio = self.speech_recognizer.record(source)

            # التعرف على الكلام
            try:
                # محاولة التعرف بالعربية أولاً
                text = self.speech_recognizer.recognize_google(audio, language='ar-SA')
            except:
                try:
                    # محاولة التعرف بالإنجليزية
                    text = self.speech_recognizer.recognize_google(audio, language='en-US')
                except:
                    return None

            # تحليل المحتوى المتعلق بالألعاب
            gaming_score = 0
            for keyword in self.config['gaming_keywords']:
                if keyword.lower() in text.lower():
                    gaming_score += 1

            metadata = {
                'language_detected': 'ar' if any(ord(c) > 127 for c in text) else 'en',
                'gaming_keywords_found': gaming_score,
                'audio_duration': 0,  # يمكن حسابها لاحقاً
                'source_type': request.media_type.value
            }

            return MediaAnalysisResult(
                media_type=request.media_type,
                analysis_type=AnalysisType.AUDIO_TRANSCRIPTION,
                content=text,
                confidence=0.8,
                metadata=metadata,
                processing_time=0
            )

        except Exception as e:
            logger.error(f"❌ خطأ في تحويل الصوت: {e}")
            return None

    async def _extract_audio_from_video(self, video_path: str) -> Optional[str]:
        """استخراج الصوت من الفيديو"""
        try:
            # إنشاء ملف مؤقت للصوت
            temp_audio = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_audio.close()

            # استخراج الصوت باستخدام moviepy
            video = VideoFileClip(video_path)
            audio = video.audio
            audio.write_audiofile(temp_audio.name, verbose=False, logger=None)

            video.close()
            audio.close()

            return temp_audio.name

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج الصوت: {e}")
            return None

    async def _translate_content(self, request: MediaAnalysisRequest) -> Optional[MediaAnalysisResult]:
        """ترجمة المحتوى"""
        try:
            if not self.translator:
                return None

            # هذه الوظيفة تحتاج محتوى نصي مستخرج مسبقاً
            # يمكن تطويرها لاحقاً للعمل مع نتائج OCR
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الترجمة: {e}")
            return None

    async def analyze_video_frames(self, video_path: str, max_frames: int = 10) -> List[MediaAnalysisResult]:
        """تحليل إطارات الفيديو"""
        try:
            results = []

            # فتح الفيديو
            cap = cv2.VideoCapture(video_path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            duration = total_frames / fps if fps > 0 else 0

            # حساب الفترة بين الإطارات
            frame_interval = max(1, total_frames // max_frames)

            frame_count = 0
            analyzed_frames = 0

            while cap.isOpened() and analyzed_frames < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break

                if frame_count % frame_interval == 0:
                    # حفظ الإطار كصورة مؤقتة
                    temp_image = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
                    cv2.imwrite(temp_image.name, frame)
                    temp_image.close()

                    # تحليل الإطار
                    frame_request = MediaAnalysisRequest(
                        media_path=temp_image.name,
                        media_type=MediaType.IMAGE,
                        analysis_types=[AnalysisType.SCENE_DESCRIPTION, AnalysisType.OCR],
                        extract_gaming_content=True
                    )

                    frame_results = await self.analyze_media(frame_request)

                    # إضافة معلومات الوقت
                    timestamp = frame_count / fps if fps > 0 else 0
                    for result in frame_results:
                        result.metadata['video_timestamp'] = timestamp
                        result.metadata['frame_number'] = frame_count
                        results.extend(frame_results)

                    # حذف الملف المؤقت
                    os.unlink(temp_image.name)
                    analyzed_frames += 1

                frame_count += 1

            cap.release()

            logger.info(f"✅ تم تحليل {analyzed_frames} إطار من الفيديو")
            return results

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل إطارات الفيديو: {e}")
            return []

    async def batch_analyze_images(self, image_paths: List[str], analysis_types: List[AnalysisType]) -> Dict[str, List[MediaAnalysisResult]]:
        """تحليل مجموعة من الصور"""
        try:
            results = {}

            for image_path in image_paths:
                request = MediaAnalysisRequest(
                    media_path=image_path,
                    media_type=MediaType.IMAGE,
                    analysis_types=analysis_types,
                    extract_gaming_content=True
                )

                image_results = await self.analyze_media(request)
                results[image_path] = image_results

            logger.info(f"✅ تم تحليل {len(image_paths)} صورة")
            return results

        except Exception as e:
            logger.error(f"❌ خطأ في التحليل المجمع: {e}")
            return {}

    async def get_gaming_content_score(self, results: List[MediaAnalysisResult]) -> float:
        """حساب نقاط المحتوى المتعلق بالألعاب"""
        try:
            if not results:
                return 0.0

            total_score = 0
            total_weight = 0

            for result in results:
                weight = 1.0
                score = 0

                # نقاط من الكلمات المفتاحية
                if 'gaming_keywords_found' in result.metadata:
                    score += result.metadata['gaming_keywords_found'] * 0.2

                # نقاط من الكائنات المكتشفة
                if 'gaming_objects' in result.metadata:
                    score += len(result.metadata['gaming_objects']) * 0.3

                # نقاط من نوع التحليل
                if result.analysis_type == AnalysisType.SCENE_DESCRIPTION:
                    weight = 1.5
                elif result.analysis_type == AnalysisType.OBJECT_DETECTION:
                    weight = 1.3
                elif result.analysis_type == AnalysisType.OCR:
                    weight = 1.1

                # نقاط من الثقة
                score *= result.confidence

                total_score += score * weight
                total_weight += weight

            final_score = total_score / total_weight if total_weight > 0 else 0
            return min(1.0, final_score)

        except Exception as e:
            logger.error(f"❌ خطأ في حساب نقاط المحتوى: {e}")
            return 0.0

    async def get_stats(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات"""
        return {
            **self.stats,
            'enabled': self.enabled,
            'models_loaded': len(self.models),
            'processors_loaded': len(self.processors),
            'success_rate': (self.stats['successful_analyses'] /
                           max(1, self.stats['total_analyses'])) * 100
        }

    async def clear_temp_files(self):
        """مسح الملفات المؤقتة"""
        try:
            temp_dir = tempfile.gettempdir()
            for file in os.listdir(temp_dir):
                if file.startswith('tmp') and (file.endswith('.jpg') or
                                              file.endswith('.png') or
                                              file.endswith('.wav')):
                    try:
                        os.unlink(os.path.join(temp_dir, file))
                    except:
                        pass
            logger.info("🧹 تم مسح الملفات المؤقتة")
        except Exception as e:
            logger.error(f"❌ خطأ في مسح الملفات المؤقتة: {e}")

# إنشاء مثيل عام
multimodal_analyzer = MultimodalAnalyzer()
