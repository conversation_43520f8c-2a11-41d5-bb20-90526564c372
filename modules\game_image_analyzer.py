# نظام تحليل صور الألعاب لإنشاء صور مشابهة
import asyncio
import aiohttp
import json
import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import hashlib
import os

from modules.logger import logger

class GameImageAnalyzer:
    """محلل صور الألعاب لفهم الشكل البصري وإنشاء صور مشابهة"""
    
    def __init__(self):
        self.cache_dir = "cache/game_images"
        self.analysis_cache_file = "cache/game_image_analysis.json"
        
        # إنشاء المجلدات
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs("cache", exist_ok=True)
        
        # تحميل التخزين المؤقت
        self.analysis_cache = self._load_analysis_cache()
        
        # قوالب تحليل الألعاب حسب النوع
        self.game_style_templates = {
            'action': {
                'visual_style': 'dynamic action scenes with explosions and combat',
                'color_palette': 'dark tones with bright highlights, red and orange accents',
                'composition': 'diagonal compositions with movement and energy',
                'characters': 'heroic poses, weapons, armor details'
            },
            'rpg': {
                'visual_style': 'fantasy landscapes with magical elements',
                'color_palette': 'rich blues, purples, golds, mystical lighting',
                'composition': 'epic wide shots with detailed environments',
                'characters': 'detailed character designs, magical effects'
            },
            'racing': {
                'visual_style': 'sleek vehicles and racing environments',
                'color_palette': 'metallic colors, bright neons, speed blur effects',
                'composition': 'dynamic angles showing speed and motion',
                'characters': 'focus on vehicles rather than people'
            },
            'sports': {
                'visual_style': 'athletic action and stadium environments',
                'color_palette': 'team colors, grass greens, bright lighting',
                'composition': 'action shots capturing peak moments',
                'characters': 'athletes in motion, sports equipment'
            },
            'strategy': {
                'visual_style': 'tactical overview with detailed units',
                'color_palette': 'earth tones, military colors, strategic blues',
                'composition': 'top-down or isometric views',
                'characters': 'armies, buildings, strategic elements'
            },
            'horror': {
                'visual_style': 'dark atmospheric scenes with tension',
                'color_palette': 'dark grays, blood reds, eerie lighting',
                'composition': 'claustrophobic or ominous wide shots',
                'characters': 'mysterious figures, monsters, shadows'
            },
            'puzzle': {
                'visual_style': 'clean geometric designs with bright colors',
                'color_palette': 'bright primary colors, clean whites',
                'composition': 'organized layouts with clear patterns',
                'characters': 'abstract shapes, minimal character design'
            }
        }
        
        logger.info("🎮 تم تهيئة محلل صور الألعاب")
    
    def _load_analysis_cache(self) -> Dict:
        """تحميل التخزين المؤقت لتحليل الصور"""
        try:
            if os.path.exists(self.analysis_cache_file):
                with open(self.analysis_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل تخزين تحليل الصور: {e}")
        
        return {}
    
    def _save_analysis_cache(self):
        """حفظ التخزين المؤقت لتحليل الصور"""
        try:
            with open(self.analysis_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ فشل في حفظ تخزين تحليل الصور: {e}")
    
    async def analyze_game_for_image_generation(self, article: Dict) -> Dict:
        """تحليل اللعبة من المقال لإنشاء صورة مناسبة"""
        try:
            game_name = self._extract_game_name(article)
            
            if not game_name:
                logger.warning("⚠️ لم يتم العثور على اسم اللعبة في المقال")
                return self._get_generic_gaming_style()
            
            # فحص التخزين المؤقت أولاً
            cache_key = hashlib.md5(game_name.lower().encode()).hexdigest()
            if cache_key in self.analysis_cache:
                logger.info(f"📦 استخدام تحليل مخزن مؤقتاً للعبة: {game_name}")
                return self.analysis_cache[cache_key]
            
            # البحث عن معلومات اللعبة
            game_info = await self._search_game_information(game_name)
            
            # تحليل النوع والأسلوب البصري
            visual_analysis = self._analyze_game_visual_style(game_info, article)
            
            # إنشاء prompt محسن للصورة
            enhanced_prompt = self._create_enhanced_image_prompt(visual_analysis, game_name, article)
            
            # حفظ في التخزين المؤقت
            result = {
                'game_name': game_name,
                'visual_style': visual_analysis,
                'enhanced_prompt': enhanced_prompt,
                'analysis_date': datetime.now().isoformat(),
                'confidence_score': visual_analysis.get('confidence', 0.7)
            }
            
            self.analysis_cache[cache_key] = result
            self._save_analysis_cache()
            
            logger.info(f"✅ تم تحليل اللعبة بنجاح: {game_name}")
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل اللعبة: {e}")
            return self._get_generic_gaming_style()
    
    def _extract_game_name(self, article: Dict) -> Optional[str]:
        """استخراج اسم اللعبة من المقال"""
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            
            # البحث عن أنماط أسماء الألعاب
            game_patterns = [
                r'لعبة\s+([^،\s]+(?:\s+[^،\s]+)*)',  # لعبة [اسم]
                r'game\s+([A-Za-z0-9\s]+?)(?:\s|$|[،.])',  # game [name]
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:game|لعبة)',  # [Name] game
                r'"([^"]+)"',  # أي شيء بين علامات اقتباس
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})\s+(?:released|announced|update)',  # أسماء قبل كلمات مفتاحية
            ]
            
            text_to_search = f"{title} {content}"
            
            for pattern in game_patterns:
                matches = re.findall(pattern, text_to_search, re.IGNORECASE)
                if matches:
                    # أخذ أطول اسم وجد
                    game_name = max(matches, key=len).strip()
                    if len(game_name) > 2 and len(game_name) < 50:
                        return game_name
            
            # إذا لم نجد شيء، نحاول استخراج من العنوان مباشرة
            title_words = title.split()
            if len(title_words) >= 2:
                # أخذ أول كلمتين كاسم محتمل للعبة
                potential_name = ' '.join(title_words[:2])
                if not any(word in potential_name.lower() for word in ['news', 'أخبار', 'update', 'تحديث']):
                    return potential_name
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في استخراج اسم اللعبة: {e}")
            return None
    
    async def _search_game_information(self, game_name: str) -> Dict:
        """البحث عن معلومات اللعبة من مصادر مختلفة"""
        try:
            # محاولة البحث في مصادر مختلفة
            game_info = {}
            
            # 1. البحث في قاعدة بيانات الألعاب المحلية (إذا كانت متوفرة)
            local_info = self._search_local_game_database(game_name)
            if local_info:
                game_info.update(local_info)
            
            # 2. تحليل النوع من الاسم والكلمات المفتاحية
            genre_analysis = self._analyze_genre_from_name(game_name)
            game_info.update(genre_analysis)
            
            return game_info
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في البحث عن معلومات اللعبة: {e}")
            return {}
    
    def _search_local_game_database(self, game_name: str) -> Dict:
        """البحث في قاعدة بيانات محلية للألعاب المشهورة"""
        # قاعدة بيانات مبسطة للألعاب المشهورة
        famous_games = {
            'mortal kombat': {'genre': 'action', 'style': 'fighting', 'theme': 'martial_arts'},
            'call of duty': {'genre': 'action', 'style': 'fps', 'theme': 'military'},
            'fifa': {'genre': 'sports', 'style': 'football', 'theme': 'soccer'},
            'minecraft': {'genre': 'sandbox', 'style': 'blocky', 'theme': 'creative'},
            'fortnite': {'genre': 'action', 'style': 'battle_royale', 'theme': 'survival'},
            'league of legends': {'genre': 'strategy', 'style': 'moba', 'theme': 'fantasy'},
            'world of warcraft': {'genre': 'rpg', 'style': 'mmorpg', 'theme': 'fantasy'},
            'grand theft auto': {'genre': 'action', 'style': 'open_world', 'theme': 'crime'},
            'the witcher': {'genre': 'rpg', 'style': 'fantasy', 'theme': 'medieval'},
            'cyberpunk': {'genre': 'rpg', 'style': 'futuristic', 'theme': 'cyberpunk'},
            'assassins creed': {'genre': 'action', 'style': 'stealth', 'theme': 'historical'},
            'resident evil': {'genre': 'horror', 'style': 'survival', 'theme': 'zombie'},
            'super mario': {'genre': 'platform', 'style': 'cartoon', 'theme': 'family'},
            'zelda': {'genre': 'rpg', 'style': 'adventure', 'theme': 'fantasy'},
            'pokemon': {'genre': 'rpg', 'style': 'cartoon', 'theme': 'creatures'},
            'street fighter': {'genre': 'action', 'style': 'fighting', 'theme': 'martial_arts'},
            'tekken': {'genre': 'action', 'style': 'fighting', 'theme': 'martial_arts'},
            'overwatch': {'genre': 'action', 'style': 'fps', 'theme': 'futuristic'},
            'apex legends': {'genre': 'action', 'style': 'battle_royale', 'theme': 'futuristic'},
            'valorant': {'genre': 'action', 'style': 'fps', 'theme': 'tactical'}
        }
        
        game_name_lower = game_name.lower()
        
        for known_game, info in famous_games.items():
            if known_game in game_name_lower or any(word in game_name_lower for word in known_game.split()):
                logger.info(f"🎮 تم العثور على اللعبة في قاعدة البيانات: {known_game}")
                return info
        
        return {}
    
    def _analyze_genre_from_name(self, game_name: str) -> Dict:
        """تحليل نوع اللعبة من الاسم والكلمات المفتاحية"""
        name_lower = game_name.lower()
        
        # كلمات مفتاحية لتحديد النوع
        genre_keywords = {
            'action': ['war', 'fight', 'combat', 'battle', 'shooter', 'gun', 'weapon', 'حرب', 'قتال', 'معركة'],
            'rpg': ['quest', 'adventure', 'magic', 'fantasy', 'dragon', 'wizard', 'مغامرة', 'سحر', 'خيال'],
            'racing': ['racing', 'car', 'speed', 'drive', 'motor', 'سباق', 'سيارة', 'سرعة'],
            'sports': ['football', 'soccer', 'basketball', 'tennis', 'sport', 'كرة', 'رياضة'],
            'strategy': ['strategy', 'empire', 'civilization', 'war', 'tactical', 'استراتيجية', 'امبراطورية'],
            'horror': ['horror', 'zombie', 'dead', 'evil', 'dark', 'رعب', 'زومبي', 'شر'],
            'puzzle': ['puzzle', 'brain', 'logic', 'match', 'لغز', 'منطق', 'ذكاء']
        }
        
        detected_genre = 'action'  # افتراضي
        confidence = 0.3
        
        for genre, keywords in genre_keywords.items():
            if any(keyword in name_lower for keyword in keywords):
                detected_genre = genre
                confidence = 0.8
                break
        
        return {
            'genre': detected_genre,
            'confidence': confidence,
            'detection_method': 'keyword_analysis'
        }
    
    def _analyze_game_visual_style(self, game_info: Dict, article: Dict) -> Dict:
        """تحليل الأسلوب البصري للعبة"""
        genre = game_info.get('genre', 'action')
        style_template = self.game_style_templates.get(genre, self.game_style_templates['action'])
        
        # تخصيص الأسلوب بناءً على معلومات إضافية
        customized_style = style_template.copy()
        
        # إضافة تفاصيل من المقال
        article_text = f"{article.get('title', '')} {article.get('content', '')}".lower()
        
        # تحسين الوصف بناءً على محتوى المقال
        if 'new' in article_text or 'جديد' in article_text:
            customized_style['composition'] += ', showcasing new features and updates'
        
        if 'character' in article_text or 'شخصية' in article_text:
            customized_style['characters'] += ', detailed character showcase'
        
        if 'trailer' in article_text or 'إعلان' in article_text:
            customized_style['visual_style'] += ', cinematic trailer style'
        
        customized_style['confidence'] = game_info.get('confidence', 0.7)
        customized_style['genre'] = genre
        
        return customized_style
    
    def _create_enhanced_image_prompt(self, visual_analysis: Dict, game_name: str, article: Dict) -> str:
        """إنشاء prompt محسن للصورة بناءً على التحليل"""
        try:
            # الأساس من التحليل البصري
            base_style = visual_analysis.get('visual_style', 'gaming artwork')
            color_palette = visual_analysis.get('color_palette', 'vibrant gaming colors')
            composition = visual_analysis.get('composition', 'dynamic gaming composition')
            characters = visual_analysis.get('characters', 'gaming characters')
            
            # إنشاء prompt شامل
            prompt_parts = [
                f"Professional gaming artwork for {game_name}",
                base_style,
                f"featuring {characters}",
                f"with {color_palette}",
                f"using {composition}",
                "high quality, detailed, 4K resolution",
                "gaming logo style, official game art aesthetic",
                "no text overlay, clean composition"
            ]
            
            # إضافة تفاصيل من العنوان
            title = article.get('title', '')
            if 'update' in title.lower() or 'تحديث' in title:
                prompt_parts.append("showing new content and updates")
            
            if 'release' in title.lower() or 'إصدار' in title:
                prompt_parts.append("launch artwork style")
            
            enhanced_prompt = ", ".join(prompt_parts)
            
            # تنظيف وتحسين الـ prompt
            enhanced_prompt = re.sub(r'\s+', ' ', enhanced_prompt).strip()
            
            logger.info(f"🎨 تم إنشاء prompt محسن: {enhanced_prompt[:100]}...")
            return enhanced_prompt
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إنشاء prompt محسن: {e}")
            return f"Professional gaming artwork for {game_name}, high quality, detailed"
    
    def _get_generic_gaming_style(self) -> Dict:
        """الحصول على أسلوب عام للألعاب عند فشل التحليل"""
        return {
            'game_name': 'Generic Game',
            'visual_style': {
                'visual_style': 'modern gaming artwork with dynamic elements',
                'color_palette': 'vibrant gaming colors with blue and orange accents',
                'composition': 'centered composition with gaming elements',
                'characters': 'gaming icons and symbols',
                'genre': 'action',
                'confidence': 0.5
            },
            'enhanced_prompt': 'Professional gaming artwork, modern style, vibrant colors, high quality, detailed, 4K resolution, gaming aesthetic',
            'analysis_date': datetime.now().isoformat(),
            'confidence_score': 0.5
        }

# إنشاء مثيل عام
game_image_analyzer = GameImageAnalyzer()
